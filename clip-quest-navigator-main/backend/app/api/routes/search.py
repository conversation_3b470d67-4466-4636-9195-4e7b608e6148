from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
import logging

from app.core.database import get_db
from app.models.video import Video, VideoFrame
from app.services.gemini_service import GeminiService
from app.services.vector_service import VectorService

logger = logging.getLogger(__name__)
router = APIRouter()

class VisualSearchRequest(BaseModel):
    video_id: int
    query: str
    max_results: int = 10

class SearchResult(BaseModel):
    timestamp: float
    confidence: float
    description: str
    frame_path: str

class VisualSearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    total_results: int

@router.post("/visual", response_model=VisualSearchResponse)
async def visual_search(
    request: Request,
    search_request: VisualSearchRequest,
    db: Session = Depends(get_db)
):
    """Perform visual search within a video"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == search_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Get video frames
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == search_request.video_id)\
            .all()
        
        if not frames:
            raise HTTPException(
                status_code=400,
                detail="No frames available for search. Video may not be processed yet."
            )
        
        # For now, implement a simple mock search
        # In production, this would use vector embeddings and similarity search
        results = await _mock_visual_search(search_request.query, frames)
        
        return VisualSearchResponse(
            query=search_request.query,
            results=results[:search_request.max_results],
            total_results=len(results)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in visual search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def _mock_visual_search(query: str, frames: List[VideoFrame]) -> List[SearchResult]:
    """Mock visual search implementation"""
    results = []
    query_lower = query.lower()
    
    # Simple keyword-based matching for demo
    for frame in frames:
        confidence = 0.0
        description = ""
        
        # Mock confidence based on query keywords
        if any(keyword in query_lower for keyword in ['person', 'people', 'man', 'woman']):
            confidence = 85.0 + (frame.timestamp % 10)  # Vary confidence
            description = f"Person detected in frame at {frame.timestamp:.1f}s"
        elif any(keyword in query_lower for keyword in ['screen', 'computer', 'display']):
            confidence = 75.0 + (frame.timestamp % 15)
            description = f"Screen/display visible at {frame.timestamp:.1f}s"
        elif any(keyword in query_lower for keyword in ['text', 'code', 'writing']):
            confidence = 70.0 + (frame.timestamp % 20)
            description = f"Text content found at {frame.timestamp:.1f}s"
        else:
            # Generic match
            confidence = 60.0 + (frame.timestamp % 25)
            description = f"Content matching '{query}' at {frame.timestamp:.1f}s"
        
        # Only include results with reasonable confidence
        if confidence > 65.0:
            results.append(SearchResult(
                timestamp=frame.timestamp,
                confidence=confidence,
                description=description,
                frame_path=frame.frame_path
            ))
    
    # Sort by confidence
    results.sort(key=lambda x: x.confidence, reverse=True)
    return results

@router.get("/{video_id}/frames")
async def get_video_frames(
    video_id: int,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get video frames for a video"""
    # Verify video exists
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Get frames
    frames = db.query(VideoFrame)\
        .filter(VideoFrame.video_id == video_id)\
        .order_by(VideoFrame.timestamp.asc())\
        .limit(limit)\
        .all()
    
    return {
        "video_id": video_id,
        "frames": [
            {
                "id": frame.id,
                "timestamp": frame.timestamp,
                "frame_path": frame.frame_path,
                "description": frame.description,
                "objects_detected": frame.objects_detected
            }
            for frame in frames
        ]
    }

@router.post("/{video_id}/analyze-frames")
async def analyze_video_frames(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Analyze video frames using AI"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Get frames that haven't been analyzed
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.is_(None))\
            .limit(10)\
            .all()  # Limit to avoid overwhelming the API
        
        if not frames:
            return {
                "message": "All frames already analyzed or no frames available",
                "analyzed_count": 0
            }
        
        gemini_service = request.app.state.gemini_service
        analyzed_count = 0
        
        for frame in frames:
            try:
                # Analyze frame with Gemini Vision
                analysis = await gemini_service.analyze_frame(
                    frame.frame_path,
                    context=f"Frame from video: {video.title}"
                )
                
                if analysis["status"] == "success":
                    frame.description = analysis["description"]
                    analyzed_count += 1
                
            except Exception as e:
                logger.error(f"Error analyzing frame {frame.id}: {e}")
                continue
        
        db.commit()
        
        return {
            "message": f"Analyzed {analyzed_count} frames",
            "analyzed_count": analyzed_count,
            "total_frames": len(frames)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing frames: {e}")
        raise HTTPException(status_code=500, detail=str(e))
