from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
import logging

from app.core.database import get_db
from app.models.video import Video, VideoFrame
from app.services.gemini_service import GeminiService
from app.services.vector_service import VectorService

logger = logging.getLogger(__name__)
router = APIRouter()

class VisualSearchRequest(BaseModel):
    video_id: int
    query: str
    max_results: int = 10

class SearchResult(BaseModel):
    timestamp: float
    confidence: float
    description: str
    frame_path: str

class VisualSearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    total_results: int

async def _mock_visual_search(query: str, frames: List[VideoFrame]) -> List[SearchResult]:
    """Mock visual search implementation (fallback when vector search is unavailable)"""
    results = []
    query_lower = query.lower()

    # Simple keyword-based matching for demo
    for frame in frames:
        confidence = 0.0
        description = ""

        # Mock some basic keyword matching
        if "person" in query_lower or "people" in query_lower:
            if frame.timestamp % 60 < 30:  # Mock: first half of each minute has people
                confidence = 0.8 + (frame.timestamp % 10) * 0.02
                description = f"Person detected at {frame.timestamp}s"
        elif "car" in query_lower or "vehicle" in query_lower:
            if frame.timestamp % 90 < 45:  # Mock: different pattern for vehicles
                confidence = 0.7 + (frame.timestamp % 15) * 0.02
                description = f"Vehicle detected at {frame.timestamp}s"
        elif "building" in query_lower or "house" in query_lower:
            if frame.timestamp % 120 < 60:  # Mock: buildings in certain intervals
                confidence = 0.6 + (frame.timestamp % 20) * 0.02
                description = f"Building detected at {frame.timestamp}s"
        else:
            # Generic object detection
            if frame.timestamp % 45 < 20:
                confidence = 0.5 + (frame.timestamp % 8) * 0.03
                description = f"Object matching '{query}' detected at {frame.timestamp}s"

        if confidence > 0.5:
            results.append(SearchResult(
                timestamp=frame.timestamp,
                confidence=confidence,
                description=description,
                frame_path=frame.frame_path
            ))

    # Sort by confidence (highest first)
    results.sort(key=lambda x: x.confidence, reverse=True)
    return results

@router.post("/visual", response_model=VisualSearchResponse)
async def visual_search(
    request: Request,
    search_request: VisualSearchRequest,
    db: Session = Depends(get_db)
):
    """Perform visual search within a video"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == search_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Initialize vector service
        vector_service = VectorService()
        if not vector_service.available:
            # Fall back to mock search when vector service is not available
            logger.warning("Vector service not available, using mock search")
            frames = db.query(VideoFrame)\
                .filter(VideoFrame.video_id == search_request.video_id)\
                .order_by(VideoFrame.timestamp.asc())\
                .all()

            mock_results = await _mock_visual_search(search_request.query, frames)
            return VisualSearchResponse(
                query=search_request.query,
                results=mock_results[:search_request.max_results],
                total_results=len(mock_results)
            )
        
        # Perform vector search
        search_results = await vector_service.search_frames(
            query=search_request.query,
            video_id=search_request.video_id,
            limit=search_request.max_results
        )
        
        # Convert vector search results to response format
        results = []
        for result in search_results:
            # Calculate confidence from distance (lower distance = higher confidence)
            confidence = max(0, 100 - (result.get('distance', 0) * 100))
            
            results.append(SearchResult(
                timestamp=result['metadata'].get('timestamp', 0),
                confidence=confidence,
                description=result['description'],
                frame_path=result['metadata'].get('frame_path', '')
            ))
        
        return VisualSearchResponse(
            query=search_request.query,
            results=results,
            total_results=len(results)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in visual search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}/frames")
async def get_video_frames(
    video_id: int,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get video frames for a video"""
    # Verify video exists
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Get frames
    frames = db.query(VideoFrame)\
        .filter(VideoFrame.video_id == video_id)\
        .order_by(VideoFrame.timestamp.asc())\
        .limit(limit)\
        .all()
    
    return {
        "video_id": video_id,
        "frames": [
            {
                "id": frame.id,
                "timestamp": frame.timestamp,
                "frame_path": frame.frame_path,
                "description": frame.description,
                "objects_detected": frame.objects_detected
            }
            for frame in frames
        ]
    }

@router.post("/{video_id}/analyze-frames")
async def analyze_video_frames(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Analyze video frames using AI and create embeddings"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Get frames that haven't been analyzed
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.is_(None))\
            .limit(10)\
            .all()  # Limit to avoid overwhelming the API
        
        if not frames:
            return {
                "message": "All frames already analyzed or no frames available",
                "analyzed_count": 0
            }
        
        gemini_service = request.app.state.gemini_service
        vector_service = VectorService()
        analyzed_count = 0
        
        for frame in frames:
            try:
                # Analyze frame with Gemini Vision
                analysis = await gemini_service.analyze_frame(
                    frame.frame_path,
                    context=f"Frame from video: {video.title}"
                )
                
                if analysis["status"] == "success":
                    frame.description = analysis["description"]
                    
                    # Add to vector database
                    if vector_service.available:
                        await vector_service.add_frame_embedding(
                            frame_id=f"frame_{frame.id}",
                            description=analysis["description"],
                            metadata={
                                "video_id": video_id,
                                "timestamp": frame.timestamp,
                                "frame_path": frame.frame_path
                            }
                        )
                    
                    analyzed_count += 1
                
            except Exception as e:
                logger.error(f"Error analyzing frame {frame.id}: {e}")
                continue
        
        db.commit()
        
        # Update video embedding status if all frames are analyzed
        total_frames = db.query(VideoFrame).filter(VideoFrame.video_id == video_id).count()
        analyzed_frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.isnot(None))\
            .count()
        
        if analyzed_frames == total_frames:
            video.embedding_status = "completed"
            db.commit()
        
        return {
            "message": f"Analyzed {analyzed_count} frames",
            "analyzed_count": analyzed_count,
            "total_frames": len(frames),
            "video_total_frames": total_frames,
            "video_analyzed_frames": analyzed_frames
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing frames: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{video_id}/index-transcript")
async def index_video_transcript(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Index video transcript for semantic search"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.transcript:
            raise HTTPException(status_code=400, detail="Video has no transcript")
        
        # Initialize vector service
        vector_service = VectorService()
        if not vector_service.available:
            raise HTTPException(
                status_code=503,
                detail="Vector search service not available"
            )
        
        # Add transcript to vector database
        success = await vector_service.add_transcript_embedding(
            video_id=str(video_id),
            transcript=video.transcript,
            metadata={
                "video_id": video_id,
                "title": video.title,
                "video_type": video.video_type
            }
        )
        
        if success:
            return {
                "message": "Transcript indexed successfully",
                "video_id": video_id
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to index transcript"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error indexing transcript: {e}")
        raise HTTPException(status_code=500, detail=str(e))
