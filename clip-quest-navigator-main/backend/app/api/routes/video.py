from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
import os
import shutil
import logging

from app.core.database import get_db
from app.models.video import Video, VideoFrame
from app.services.youtube_service import YouTubeService
from app.core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/upload")
async def upload_video(
    request: Request,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload and process a video file"""
    try:
        # Validate file size
        if file.size > settings.MAX_VIDEO_SIZE_MB * 1024 * 1024:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size is {settings.MAX_VIDEO_SIZE_MB}MB"
            )

        # Validate file type
        if not file.content_type.startswith('video/'):
            raise HTTPException(
                status_code=400,
                detail="File must be a video"
            )

        # Create video record
        video = Video(
            title=file.filename,
            url="",
            video_type="upload",
            status="processing"
        )
        db.add(video)
        db.commit()
        db.refresh(video)

        # Save uploaded file
        file_path = f"{settings.UPLOAD_DIR}/video_{video.id}.mp4"
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        video.file_path = file_path
        db.commit()

        # Process video in background (for now, process immediately)
        video_processor = request.app.state.video_processor
        result = await video_processor.process_uploaded_video(file_path, video.id)

        if result["status"] == "success":
            # Save frame information
            for frame_data in result["frames"]:
                frame = VideoFrame(
                    video_id=video.id,
                    timestamp=frame_data["timestamp"],
                    frame_path=frame_data["frame_path"]
                )
                db.add(frame)

            video.status = "completed"
            video.frame_count = result["frame_count"]
            video.duration = result["video_info"].get("duration")
        else:
            video.status = "failed"

        db.commit()

        return {
            "video_id": video.id,
            "status": video.status,
            "message": "Video uploaded and processed successfully" if result["status"] == "success" else "Video upload failed"
        }

    except Exception as e:
        logger.error(f"Error uploading video: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class YouTubeRequest(BaseModel):
    youtube_url: str

@router.post("/youtube")
async def process_youtube_video(
    request: Request,
    youtube_request: YouTubeRequest,
    db: Session = Depends(get_db)
):
    """Process a YouTube video"""
    try:
        youtube_service = YouTubeService()
        gemini_service = request.app.state.gemini_service

        # Extract video ID
        video_id = youtube_service.extract_video_id(youtube_request.youtube_url)
        if not video_id:
            raise HTTPException(status_code=400, detail="Invalid YouTube URL")

        # Get video info
        video_info = await youtube_service.get_video_info(video_id)

        # Create video record
        video = Video(
            title=video_info["title"],
            url=youtube_request.youtube_url,
            video_type="youtube",
            youtube_id=video_id,
            status="processing"
        )
        db.add(video)
        db.commit()
        db.refresh(video)

        # Get transcript
        try:
            transcript_data = youtube_service.get_transcript(video_id)
            video.transcript = transcript_data["text"]

            # Generate sections using AI
            sections = await gemini_service.generate_video_sections(
                transcript_data["text"],
                video_info
            )
            video.sections = sections

            video.status = "completed"

        except Exception as e:
            logger.warning(f"Could not get transcript for {video_id}: {e}")
            video.status = "completed"  # Still mark as completed even without transcript

        db.commit()

        return {
            "video_id": video.id,
            "status": video.status,
            "title": video.title,
            "has_transcript": bool(video.transcript),
            "sections_count": len(video.sections) if video.sections else 0
        }

    except Exception as e:
        logger.error(f"Error processing YouTube video: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}")
async def get_video(video_id: int, db: Session = Depends(get_db)):
    """Get video information"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    return {
        "id": video.id,
        "title": video.title,
        "url": video.url,
        "video_type": video.video_type,
        "status": video.status,
        "duration": video.duration,
        "has_transcript": bool(video.transcript),
        "sections": video.sections,
        "frame_count": video.frame_count,
        "created_at": video.created_at
    }

@router.get("/{video_id}/sections")
async def get_video_sections(video_id: int, db: Session = Depends(get_db)):
    """Get video sections"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    return {
        "video_id": video.id,
        "sections": video.sections or []
    }

@router.delete("/{video_id}")
async def delete_video(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Delete a video and its associated files"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    try:
        # Clean up files
        video_processor = request.app.state.video_processor
        video_processor.cleanup_video_files(video_id)

        # Delete from database
        db.query(VideoFrame).filter(VideoFrame.video_id == video_id).delete()
        db.delete(video)
        db.commit()

        return {"message": "Video deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
