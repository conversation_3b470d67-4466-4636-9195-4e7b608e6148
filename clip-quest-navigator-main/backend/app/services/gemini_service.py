import google.generativeai as genai
import logging
from typing import List, Dict, Optional
from app.core.config import settings

logger = logging.getLogger(__name__)

class GeminiService:
    """Service for interacting with Google's Gemini AI"""
    
    def __init__(self):
        if not settings.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY is required")
        
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model = genai.GenerativeModel(settings.GEMINI_MODEL)
        
    async def analyze_video_content(self, transcript: str, video_info: Dict) -> Dict:
        """Analyze video content and generate insights"""
        try:
            prompt = f"""
            Analyze this video transcript and provide insights:
            
            Video Title: {video_info.get('title', 'Unknown')}
            Transcript: {transcript}
            
            Please provide:
            1. A comprehensive summary (2-3 sentences)
            2. Key topics discussed (list of 5-7 topics)
            3. Main themes and concepts
            4. Important timestamps and what happens at those times
            
            Format your response as JSON with keys: summary, key_topics, themes, important_moments
            """
            
            response = self.model.generate_content(prompt)
            return {
                "analysis": response.text,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error analyzing video content: {e}")
            return {
                "analysis": None,
                "status": "error",
                "error": str(e)
            }
    
    async def generate_chat_response(self, 
                                   question: str, 
                                   transcript: str, 
                                   video_info: Dict,
                                   chat_history: List[Dict] = None) -> Dict:
        """Generate a chat response based on video content"""
        try:
            # Build context from chat history
            history_context = ""
            if chat_history:
                for msg in chat_history[-5:]:  # Last 5 messages for context
                    history_context += f"User: {msg.get('message', '')}\nAI: {msg.get('response', '')}\n"
            
            prompt = f"""
            You are an AI assistant that helps users understand video content. 
            
            Video Information:
            Title: {video_info.get('title', 'Unknown')}
            Transcript: {transcript}
            
            Previous conversation:
            {history_context}
            
            Current question: {question}
            
            Please provide a helpful response based on the video content. If you reference specific parts of the video, include timestamps in your response. Format timestamps as [MM:SS] or [HH:MM:SS].
            
            Also identify any specific timestamps that support your answer and return them separately.
            """
            
            response = self.model.generate_content(prompt)
            
            # Extract timestamps from response
            citations = self._extract_timestamps(response.text, transcript)
            
            return {
                "response": response.text,
                "citations": citations,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error generating chat response: {e}")
            return {
                "response": "I'm sorry, I encountered an error while processing your question. Please try again.",
                "citations": [],
                "status": "error",
                "error": str(e)
            }
    
    async def generate_video_sections(self, transcript: str, video_info: Dict) -> List[Dict]:
        """Generate intelligent video sections using AI"""
        try:
            prompt = f"""
            Analyze this video transcript and create logical sections with timestamps:
            
            Video Title: {video_info.get('title', 'Unknown')}
            Transcript: {transcript}
            
            Create 4-8 logical sections that represent different topics or phases of the video.
            For each section, provide:
            - A descriptive title
            - Start and end timestamps
            - A brief description
            - 2-3 key topics covered
            
            Format as JSON array with objects containing: title, start_time, end_time, description, key_topics
            """
            
            response = self.model.generate_content(prompt)
            
            # Parse the response and extract sections
            # For now, return a simple structure
            return self._parse_sections_response(response.text)
            
        except Exception as e:
            logger.error(f"Error generating video sections: {e}")
            return []
    
    async def analyze_frame(self, frame_path: str, context: str = "") -> Dict:
        """Analyze a video frame using Gemini Vision"""
        try:
            # Upload the image
            image_file = genai.upload_file(frame_path)
            
            prompt = f"""
            Analyze this video frame and describe what you see.
            
            Context: {context}
            
            Please provide:
            1. A detailed description of the visual content
            2. Any text visible in the frame
            3. Objects, people, or scenes present
            4. The overall context or setting
            
            Be specific and detailed in your description.
            """
            
            response = self.model.generate_content([prompt, image_file])
            
            return {
                "description": response.text,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error analyzing frame: {e}")
            return {
                "description": "Unable to analyze frame",
                "status": "error",
                "error": str(e)
            }
    
    def _extract_timestamps(self, text: str, transcript: str) -> List[Dict]:
        """Extract timestamp citations from AI response"""
        import re
        
        # Find timestamp patterns in the response
        timestamp_pattern = r'\[(\d{1,2}:\d{2}(?::\d{2})?)\]'
        matches = re.findall(timestamp_pattern, text)
        
        citations = []
        for match in matches:
            # Convert timestamp to seconds
            time_parts = match.split(':')
            if len(time_parts) == 2:  # MM:SS
                seconds = int(time_parts[0]) * 60 + int(time_parts[1])
            else:  # HH:MM:SS
                seconds = int(time_parts[0]) * 3600 + int(time_parts[1]) * 60 + int(time_parts[2])
            
            citations.append({
                "text": f"Reference at {match}",
                "time": seconds
            })
        
        return citations
    
    def _parse_sections_response(self, response_text: str) -> List[Dict]:
        """Parse AI response to extract video sections"""
        # Simple parsing - in production, use more robust JSON parsing
        try:
            import json
            # Try to extract JSON from response
            start = response_text.find('[')
            end = response_text.rfind(']') + 1
            if start != -1 and end != 0:
                json_str = response_text[start:end]
                return json.loads(json_str)
        except:
            pass
        
        # Fallback: return empty list
        return []
