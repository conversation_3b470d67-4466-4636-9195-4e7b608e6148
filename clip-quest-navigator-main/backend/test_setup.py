#!/usr/bin/env python3
"""
Test script to verify backend setup and dependencies
"""
import sys
import os
from pathlib import Path

def test_imports():
    """Test if all required packages can be imported"""
    print("🧪 Testing Python imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn imported successfully")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
    
    try:
        import google.generativeai as genai
        print("✅ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"❌ Google Generative AI import failed: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV imported successfully")
    except ImportError as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        import chromadb
        print("✅ ChromaDB imported successfully")
    except ImportError as e:
        print(f"❌ ChromaDB import failed: {e}")
        return False
    
    try:
        from youtube_transcript_api import YouTubeTranscriptApi
        print("✅ YouTube Transcript API imported successfully")
    except ImportError as e:
        print(f"❌ YouTube Transcript API import failed: {e}")
        return False
    
    return True

def test_environment():
    """Test environment configuration"""
    print("\n🔧 Testing environment configuration...")
    
    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
    else:
        print("⚠️  .env file not found - copy .env.example to .env")
        return False
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        gemini_key = os.getenv("GEMINI_API_KEY")
        if gemini_key and gemini_key != "your_gemini_api_key_here":
            print("✅ GEMINI_API_KEY is configured")
        else:
            print("⚠️  GEMINI_API_KEY not configured properly")
            return False
            
    except ImportError:
        print("❌ python-dotenv not installed")
        return False
    
    return True

def test_directories():
    """Test required directories"""
    print("\n📁 Testing directory structure...")
    
    required_dirs = [
        "uploads",
        "uploads/frames", 
        "chroma_db"
    ]
    
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"✅ {dir_path} directory exists")
        else:
            print(f"📁 Creating {dir_path} directory...")
            path.mkdir(parents=True, exist_ok=True)
            print(f"✅ {dir_path} directory created")
    
    return True

def test_app_imports():
    """Test application imports"""
    print("\n🚀 Testing application imports...")
    
    try:
        from app.core.config import settings
        print("✅ App configuration imported successfully")
    except ImportError as e:
        print(f"❌ App configuration import failed: {e}")
        return False
    
    try:
        from app.services.youtube_service import YouTubeService
        print("✅ YouTube service imported successfully")
    except ImportError as e:
        print(f"❌ YouTube service import failed: {e}")
        return False
    
    try:
        from app.services.gemini_service import GeminiService
        print("✅ Gemini service imported successfully")
    except ImportError as e:
        print(f"❌ Gemini service import failed: {e}")
        return False
    
    return True

def test_gemini_connection():
    """Test Gemini API connection"""
    print("\n🤖 Testing Gemini API connection...")
    
    try:
        from app.services.gemini_service import GeminiService
        gemini_service = GeminiService()
        print("✅ Gemini service initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Gemini service initialization failed: {e}")
        print("💡 Make sure your GEMINI_API_KEY is valid")
        return False

def main():
    """Run all tests"""
    print("🔍 VideoChat AI Backend Setup Test")
    print("=" * 40)
    
    tests = [
        ("Package Imports", test_imports),
        ("Environment Config", test_environment),
        ("Directory Structure", test_directories),
        ("Application Imports", test_app_imports),
        ("Gemini Connection", test_gemini_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 40)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 All tests passed! Your backend is ready to run.")
        print("💡 Start the server with: python start.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
        print("💡 Check the SETUP.md file for detailed instructions.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
