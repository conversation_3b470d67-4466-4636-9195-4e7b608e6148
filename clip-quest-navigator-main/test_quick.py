#!/usr/bin/env python3
"""
Quick test to check video processing and chat
"""
import requests
import json

BACKEND_URL = "http://localhost:8000"

def test_video_info():
    """Check what videos we have"""
    try:
        # Get video 1 info
        response = requests.get(f"{BACKEND_URL}/api/v1/video/1", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("📹 Video 1 Info:")
            print(f"   Title: {data.get('title')}")
            print(f"   Status: {data.get('status')}")
            print(f"   Has Transcript: {data.get('has_transcript')}")
            print(f"   Frame Count: {data.get('frame_count')}")
            print(f"   Sections: {len(data.get('sections', []))}")
            return data
        else:
            print(f"❌ Failed to get video info: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_frames():
    """Check frames for video 1"""
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/search/1/frames", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"\n🎬 Frames for Video 1:")
            print(f"   Total frames: {len(data.get('frames', []))}")
            for frame in data.get('frames', [])[:3]:
                print(f"   - {frame.get('timestamp')}s: {frame.get('description', 'No description')}")
            return len(data.get('frames', []))
        else:
            print(f"❌ Failed to get frames: {response.status_code}")
            return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 0

def test_simple_chat():
    """Test simple chat"""
    try:
        payload = {
            "video_id": 1,
            "message": "Hello, what can you tell me about this video?"
        }
        response = requests.post(
            f"{BACKEND_URL}/api/v1/chat/message",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n💬 Chat Response:")
            print(f"   Response: {data.get('response')}")
            print(f"   Citations: {len(data.get('citations', []))}")
            return True
        else:
            print(f"❌ Chat failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat error: {e}")
        return False

def main():
    print("🔍 Quick VideoChat AI Test")
    print("=" * 30)
    
    # Test video info
    video_info = test_video_info()
    
    # Test frames
    frame_count = test_frames()
    
    # Test chat
    chat_success = test_simple_chat()
    
    print("\n" + "=" * 30)
    print("📊 Quick Test Summary:")
    print(f"✅ Video Info: {'Available' if video_info else 'Failed'}")
    print(f"✅ Frames: {frame_count} found")
    print(f"✅ Chat: {'Working' if chat_success else 'Failed'}")

if __name__ == "__main__":
    main()
