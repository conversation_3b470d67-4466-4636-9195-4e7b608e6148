#!/usr/bin/env python3
"""
System Integration Test for VideoChat AI
Tests the complete pipeline from YouTube video processing to chat functionality
"""
import requests
import time
import json

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:8081"

def test_backend_health():
    """Test if backend is running and healthy"""
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend Health Check:")
            print(f"   Status: {data.get('status')}")
            for service, status in data.get('services', {}).items():
                print(f"   {service}: {status}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False

def test_frontend_accessibility():
    """Test if frontend is accessible"""
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend not accessible: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend not accessible: {e}")
        return False

def test_youtube_processing():
    """Test YouTube video processing"""
    try:
        # Test with a short, public YouTube video
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll (short, always available)
        
        payload = {"youtube_url": test_url}
        response = requests.post(
            f"{BACKEND_URL}/api/v1/video/youtube", 
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ YouTube Processing:")
            print(f"   Video ID: {data.get('video_id')}")
            print(f"   Title: {data.get('title')}")
            print(f"   Has Transcript: {data.get('has_transcript')}")
            print(f"   Sections: {data.get('sections_count')}")
            return data.get('video_id')
        else:
            print(f"❌ YouTube processing failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ YouTube processing error: {e}")
        return None

def test_chat_functionality(video_id):
    """Test chat functionality with a video"""
    if not video_id:
        print("⏭️  Skipping chat test (no video ID)")
        return False
        
    try:
        payload = {
            "video_id": video_id,
            "message": "What is this video about?"
        }
        response = requests.post(
            f"{BACKEND_URL}/api/v1/chat/message",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Chat Functionality:")
            print(f"   Response: {data.get('response')[:100]}...")
            print(f"   Citations: {len(data.get('citations', []))} found")
            return True
        else:
            print(f"❌ Chat failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat error: {e}")
        return False

def test_visual_search(video_id):
    """Test visual search functionality"""
    if not video_id:
        print("⏭️  Skipping visual search test (no video ID)")
        return False
        
    try:
        payload = {
            "video_id": video_id,
            "query": "person",
            "max_results": 5
        }
        response = requests.post(
            f"{BACKEND_URL}/api/v1/search/visual",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Visual Search:")
            print(f"   Query: {data.get('query')}")
            print(f"   Results: {data.get('total_results')} found")
            for i, result in enumerate(data.get('results', [])[:3]):
                print(f"   [{i+1}] {result.get('timestamp')}s - {result.get('confidence'):.1f}% confidence")
            return True
        else:
            print(f"❌ Visual search failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Visual search error: {e}")
        return False

def main():
    """Run all system tests"""
    print("🧪 VideoChat AI System Integration Test")
    print("=" * 50)
    
    # Test results
    results = []
    
    # 1. Backend Health
    results.append(("Backend Health", test_backend_health()))
    
    # 2. Frontend Accessibility
    results.append(("Frontend Access", test_frontend_accessibility()))
    
    # 3. YouTube Processing
    print("\n📹 Testing YouTube Video Processing...")
    video_id = test_youtube_processing()
    results.append(("YouTube Processing", video_id is not None))
    
    # Wait a moment for processing
    if video_id:
        print("⏳ Waiting for video processing to complete...")
        time.sleep(3)
    
    # 4. Chat Functionality
    print("\n💬 Testing Chat Functionality...")
    results.append(("Chat Functionality", test_chat_functionality(video_id)))
    
    # 5. Visual Search
    print("\n🔍 Testing Visual Search...")
    results.append(("Visual Search", test_visual_search(video_id)))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, passed_test in results:
        status = "✅ PASS" if passed_test else "❌ FAIL"
        print(f"{status} {test_name}")
        if passed_test:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All systems operational! VideoChat AI is ready to use.")
        print("\n🚀 Access the application:")
        print(f"   Frontend: {FRONTEND_URL}")
        print(f"   Backend API: {BACKEND_URL}")
        print(f"   API Docs: {BACKEND_URL}/docs")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
