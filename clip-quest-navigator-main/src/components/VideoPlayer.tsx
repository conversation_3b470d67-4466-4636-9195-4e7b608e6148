
import React, { useRef, useEffect } from 'react';
import { Play, Pause, Volume2, Maximize, RotateCcw } from 'lucide-react';

interface VideoPlayerProps {
  videoUrl: string;
  title: string;
  currentTime?: number;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoUrl, title, currentTime = 0 }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current && currentTime > 0) {
      videoRef.current.currentTime = currentTime;
    }
  }, [currentTime]);

  const isYouTubeUrl = (url: string) => {
    return url.includes('youtube.com') || url.includes('youtu.be');
  };

  const getYouTubeEmbedUrl = (url: string) => {
    const videoId = extractYouTubeVideoId(url);
    return videoId ? `https://www.youtube.com/embed/${videoId}?enablejsapi=1` : url;
  };

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 overflow-hidden">
      {/* Video Header */}
      <div className="px-6 py-4 border-b border-white/10">
        <h2 className="text-xl font-semibold text-white truncate">{title}</h2>
        <p className="text-purple-200 text-sm mt-1">
          Ready for AI analysis and chat
        </p>
      </div>

      {/* Video Container */}
      <div className="relative aspect-video bg-black">
        {isYouTubeUrl(videoUrl) ? (
          <iframe
            src={getYouTubeEmbedUrl(videoUrl)}
            className="w-full h-full"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title={title}
          />
        ) : (
          <video
            ref={videoRef}
            src={videoUrl}
            controls
            className="w-full h-full object-contain"
            preload="metadata"
          >
            Your browser does not support the video tag.
          </video>
        )}

        {/* Custom Overlay Controls (for future enhancement) */}
        <div className="absolute bottom-4 left-4 right-4 bg-black/50 rounded-lg p-3 opacity-0 hover:opacity-100 transition-opacity duration-300">
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center gap-3">
              <button className="hover:text-purple-400 transition-colors">
                <Play className="w-5 h-5" />
              </button>
              <button className="hover:text-purple-400 transition-colors">
                <Volume2 className="w-5 h-5" />
              </button>
              <span className="text-sm">00:00 / 00:00</span>
            </div>
            
            <div className="flex items-center gap-2">
              <button className="hover:text-purple-400 transition-colors">
                <RotateCcw className="w-4 h-4" />
              </button>
              <button className="hover:text-purple-400 transition-colors">
                <Maximize className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Video Info */}
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-400 font-medium">
              AI Analysis Active
            </span>
          </div>
          <div className="text-sm text-purple-200">
            Ready for chat and search
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
