
import React from 'react';
import { Clock, Play, Hash } from 'lucide-react';

interface VideoSection {
  id: string;
  title: string;
  startTime: number;
  endTime: number;
  description: string;
  keyTopics: string[];
}

interface VideoSectionsProps {
  videoUrl: string;
  onTimeJump: (time: number) => void;
}

const VideoSections: React.FC<VideoSectionsProps> = ({ videoUrl, onTimeJump }) => {
  // Mock data - in real implementation, this would come from AI analysis
  const sections: VideoSection[] = [
    {
      id: '1',
      title: 'Introduction & Background',
      startTime: 0,
      endTime: 45,
      description: 'Speaker introduces themselves and their background working at Microsoft and Google',
      keyTopics: ['Speaker background', 'Big Tech experience', 'Exaflop consulting']
    },
    {
      id: '2',
      title: 'Project Overview',
      startTime: 45,
      endTime: 120,
      description: 'Overview of the video analysis project and its main objectives',
      keyTopics: ['Video data', 'Query and search', 'AI analysis']
    },
    {
      id: '3',
      title: 'Chat with Videos Feature',
      startTime: 120,
      endTime: 210,
      description: 'Detailed explanation of the RAG-based video chat functionality',
      keyTopics: ['RAG implementation', 'Video upload', 'Chat interface']
    },
    {
      id: '4',
      title: 'Timestamp Navigation',
      startTime: 210,
      endTime: 300,
      description: 'How to implement section breakdown and timestamp hyperlinks',
      keyTopics: ['Video sections', 'Hyperlink timestamps', 'Navigation']
    },
    {
      id: '5',
      title: 'Visual Search Capabilities',
      startTime: 300,
      endTime: 380,
      description: 'Natural language search within video frames and visual content',
      keyTopics: ['Visual search', 'Frame analysis', 'Natural language queries']
    },
    {
      id: '6',
      title: 'Conclusion',
      startTime: 380,
      endTime: 420,
      description: 'Wrap-up and final thoughts on the project',
      keyTopics: ['Project summary', 'Next steps', 'Closing remarks']
    }
  ];

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDuration = (startTime: number, endTime: number): string => {
    const duration = endTime - startTime;
    return `${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-white mb-2">Video Sections</h3>
        <p className="text-sm text-purple-200">
          AI-generated breakdown with clickable timestamps
        </p>
      </div>

      {/* Sections List */}
      <div className="space-y-3">
        {sections.map((section, index) => (
          <div
            key={section.id}
            className="bg-white/10 rounded-lg border border-white/20 p-4 hover:bg-white/15 transition-all duration-300 cursor-pointer group"
            onClick={() => onTimeJump(section.startTime)}
          >
            {/* Section Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  {index + 1}
                </div>
                <div>
                  <h4 className="font-medium text-white group-hover:text-purple-300 transition-colors">
                    {section.title}
                  </h4>
                  <div className="flex items-center gap-4 text-xs text-purple-300 mt-1">
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {formatTime(section.startTime)}
                    </span>
                    <span>Duration: {getDuration(section.startTime, section.endTime)}</span>
                  </div>
                </div>
              </div>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onTimeJump(section.startTime);
                }}
                className="opacity-0 group-hover:opacity-100 transition-opacity bg-purple-500 hover:bg-purple-600 text-white p-2 rounded-full transform hover:scale-110 transition-transform"
              >
                <Play className="w-4 h-4" />
              </button>
            </div>

            {/* Section Description */}
            <p className="text-sm text-purple-200 mb-3 leading-relaxed">
              {section.description}
            </p>

            {/* Key Topics */}
            <div className="flex flex-wrap gap-2">
              {section.keyTopics.map((topic, topicIndex) => (
                <span
                  key={topicIndex}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-white/10 rounded text-xs text-purple-300 border border-white/20"
                >
                  <Hash className="w-3 h-3" />
                  {topic}
                </span>
              ))}
            </div>

            {/* Progress Bar */}
            <div className="mt-3 h-1 bg-white/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
                style={{ 
                  width: `${((section.endTime - section.startTime) / 420) * 100}%` 
                }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Timeline Overview */}
      <div className="mt-6 p-4 bg-white/10 rounded-lg border border-white/20">
        <h4 className="text-sm font-medium text-white mb-3">Timeline Overview</h4>
        <div className="relative h-2 bg-white/20 rounded-full">
          {sections.map((section) => (
            <div
              key={section.id}
              className="absolute top-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full cursor-pointer hover:opacity-80 transition-opacity"
              style={{
                left: `${(section.startTime / 420) * 100}%`,
                width: `${((section.endTime - section.startTime) / 420) * 100}%`,
              }}
              onClick={() => onTimeJump(section.startTime)}
              title={`${section.title} (${formatTime(section.startTime)})`}
            />
          ))}
        </div>
        <div className="flex justify-between text-xs text-purple-300 mt-2">
          <span>0:00</span>
          <span>7:00</span>
        </div>
      </div>
    </div>
  );
};

export default VideoSections;
