
import React, { useState } from 'react';
import { Search, Eye, Clock, Filter, Zap } from 'lucide-react';
import { apiService, SearchResult } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface VisualSearchProps {
  videoId: number;
  videoUrl: string;
  onTimeJump: (time: number) => void;
}

const VisualSearch: React.FC<VisualSearchProps> = ({ videoId, videoUrl, onTimeJump }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const { toast } = useToast();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setHasSearched(true);

    try {
      const response = await apiService.visualSearch(videoId, searchQuery);
      setSearchResults(response.results);

      if (response.results.length === 0) {
        toast({
          title: "No results found",
          description: `No visual content matching "${searchQuery}" was found in this video.`,
        });
      }
    } catch (error) {
      toast({
        title: "Search failed",
        description: error instanceof Error ? error.message : "Failed to search video content",
        variant: "destructive",
      });

      // Fallback to mock results for demo
      const mockResults: SearchResult[] = generateMockResults(searchQuery);
      setSearchResults(mockResults);
    } finally {
      setIsSearching(false);
    }
  };

  const generateMockResults = (query: string): SearchResult[] => {
    const lowerQuery = query.toLowerCase();

    if (lowerQuery.includes('person') || lowerQuery.includes('speaker') || lowerQuery.includes('man')) {
      return [
        {
          timestamp: 15,
          confidence: 95,
          description: 'Speaker introducing himself at the beginning',
          frame_path: '/api/placeholder/120/68'
        },
        {
          timestamp: 45,
          confidence: 92,
          description: 'Speaker explaining his background',
          frame_path: '/api/placeholder/120/68'
        },
        {
          timestamp: 180,
          confidence: 88,
          description: 'Speaker demonstrating the project features',
          frame_path: '/api/placeholder/120/68'
        }
      ];
    }

    if (lowerQuery.includes('screen') || lowerQuery.includes('display') || lowerQuery.includes('computer')) {
      return [
        {
          id: '1',
          timestamp: 120,
          confidence: 89,
          description: 'Computer screen showing project overview',
          thumbnail: '/api/placeholder/120/68'
        },
        {
          id: '2',
          timestamp: 240,
          confidence: 85,
          description: 'Display showing video analysis interface',
          thumbnail: '/api/placeholder/120/68'
        }
      ];
    }

    if (lowerQuery.includes('text') || lowerQuery.includes('code') || lowerQuery.includes('diagram')) {
      return [
        {
          id: '1',
          timestamp: 90,
          confidence: 82,
          description: 'Text showing project requirements',
          thumbnail: '/api/placeholder/120/68'
        },
        {
          id: '2',
          timestamp: 200,
          confidence: 78,
          description: 'Code examples and implementation details',
          thumbnail: '/api/placeholder/120/68'
        }
      ];
    }

    // Default results for any query
    return [
      {
        id: '1',
        timestamp: 60,
        confidence: 75,
        description: `Content related to "${query}" found in video`,
        thumbnail: '/api/placeholder/120/68'
      },
      {
        id: '2',
        timestamp: 150,
        confidence: 68,
        description: `Another instance of "${query}" detected`,
        thumbnail: '/api/placeholder/120/68'
      }
    ];
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 90) return 'text-green-400';
    if (confidence >= 75) return 'text-yellow-400';
    return 'text-orange-400';
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-white mb-2">Visual Search</h3>
        <p className="text-sm text-purple-200">
          Search for objects, people, or scenes within video frames
        </p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Describe what you're looking for... (e.g., 'red car', 'person speaking', 'computer screen')"
            className="w-full px-4 py-3 pl-12 bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isSearching}
          />
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-purple-300" />
        </div>

        <button
          type="submit"
          disabled={!searchQuery.trim() || isSearching}
          className="w-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center gap-2"
        >
          {isSearching ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
              Analyzing Frames...
            </>
          ) : (
            <>
              <Eye className="w-5 h-5" />
              Search Video
            </>
          )}
        </button>
      </form>

      {/* Search Results */}
      {hasSearched && (
        <div className="space-y-4">
          {isSearching ? (
            <div className="text-center py-8">
              <div className="animate-pulse space-y-4">
                <div className="w-16 h-16 bg-white/20 rounded-full mx-auto flex items-center justify-center">
                  <Zap className="w-8 h-8 text-purple-400" />
                </div>
                <p className="text-purple-200">
                  AI is analyzing video frames for "{searchQuery}"
                </p>
              </div>
            </div>
          ) : searchResults.length > 0 ? (
            <>
              <div className="flex items-center justify-between">
                <h4 className="text-white font-medium">
                  Found {searchResults.length} matches for "{searchQuery}"
                </h4>
                <div className="flex items-center gap-2 text-sm text-purple-300">
                  <Filter className="w-4 h-4" />
                  Sorted by confidence
                </div>
              </div>

              <div className="space-y-3">
                {searchResults.map((result) => (
                  <div
                    key={result.id}
                    className="bg-white/10 rounded-lg border border-white/20 p-4 hover:bg-white/15 transition-all duration-300 cursor-pointer group"
                    onClick={() => onTimeJump(result.timestamp)}
                  >
                    <div className="flex gap-4">
                      {/* Thumbnail */}
                      <div className="w-20 h-12 bg-gray-700 rounded flex items-center justify-center flex-shrink-0">
                        <Eye className="w-6 h-6 text-gray-400" />
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <p className="text-white text-sm leading-relaxed group-hover:text-purple-300 transition-colors">
                            {result.description}
                          </p>
                          <div className="flex items-center gap-2 text-xs">
                            <span className={`font-medium ${getConfidenceColor(result.confidence)}`}>
                              {result.confidence}%
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-xs text-purple-300">
                            <Clock className="w-3 h-3" />
                            <span>{formatTime(result.timestamp)}</span>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onTimeJump(result.timestamp);
                            }}
                            className="opacity-0 group-hover:opacity-100 transition-opacity bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs font-medium"
                          >
                            Jump to frame
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-purple-200 mb-2">
                No matches found for "{searchQuery}"
              </p>
              <p className="text-sm text-purple-300">
                Try different keywords or be more specific about visual elements
              </p>
            </div>
          )}
        </div>
      )}

      {/* Search Tips */}
      {!hasSearched && (
        <div className="mt-6 p-4 bg-white/10 rounded-lg border border-white/20">
          <h4 className="text-sm font-medium text-white mb-3">Search Tips</h4>
          <ul className="space-y-2 text-sm text-purple-200">
            <li>• Try specific objects: "red car", "laptop", "coffee cup"</li>
            <li>• Search for people: "person speaking", "woman in blue shirt"</li>
            <li>• Look for scenes: "outdoor scene", "office setting", "sunset"</li>
            <li>• Find text: "code on screen", "presentation slide", "whiteboard"</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default VisualSearch;
