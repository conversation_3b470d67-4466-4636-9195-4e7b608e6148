
import React, { useState, useRef } from 'react';
import { Upload, Link, Play, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiService } from '@/services/api';

interface VideoUploadProps {
  onVideoUpload: (videoId: number, videoUrl: string, title: string) => void;
}

const VideoUpload: React.FC<VideoUploadProps> = ({ onVideoUpload }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    const videoFile = files.find(file => file.type.startsWith('video/'));

    if (videoFile) {
      handleFileUpload(videoFile);
    } else {
      toast({
        title: "Invalid file type",
        description: "Please upload a video file",
        variant: "destructive",
      });
    }
  };

  const handleFileUpload = async (file: File) => {
    setIsProcessing(true);

    try {
      const result = await apiService.uploadVideo(file);

      if (result.status === 'completed') {
        onVideoUpload(result.video_id, URL.createObjectURL(file), file.name);
        toast({
          title: "Video uploaded successfully!",
          description: "You can now chat with your video",
        });
      } else {
        toast({
          title: "Video processing...",
          description: "Your video is being processed. Please wait.",
        });
      }
    } catch (error) {
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload video",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleYouTubeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!youtubeUrl.trim()) return;

    setIsProcessing(true);

    try {
      const result = await apiService.processYouTubeVideo(youtubeUrl);

      onVideoUpload(result.video_id, youtubeUrl, result.title);
      toast({
        title: "YouTube video loaded!",
        description: `Processing complete. ${result.has_transcript ? 'Transcript available.' : 'No transcript found.'}`,
      });
    } catch (error) {
      toast({
        title: "Failed to process YouTube video",
        description: error instanceof Error ? error.message : "Please check the URL and try again",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  if (isProcessing) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-12 text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-500 border-t-transparent mx-auto mb-6"></div>
          <h2 className="text-2xl font-bold text-white mb-2">Processing Video...</h2>
          <p className="text-purple-200">
            We're analyzing your video and preparing it for AI chat
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* File Upload Area */}
      <div
        className={`relative bg-white/10 backdrop-blur-lg rounded-2xl border-2 border-dashed transition-all duration-300 p-12 text-center ${
          isDragging
            ? 'border-purple-400 bg-purple-500/20 scale-105'
            : 'border-white/30 hover:border-purple-400 hover:bg-white/5'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="video/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        <Upload className="w-16 h-16 text-purple-400 mx-auto mb-6" />
        <h2 className="text-2xl font-bold text-white mb-4">
          Upload Your Video
        </h2>
        <p className="text-purple-200 mb-6 max-w-md mx-auto">
          Drag and drop your video file here, or click to browse.
          Supports MP4, WebM, AVI, and more.
        </p>

        <button
          onClick={() => fileInputRef.current?.click()}
          className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
        >
          Choose File
        </button>
      </div>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-white/20"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-4 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-purple-200">
            or
          </span>
        </div>
      </div>

      {/* YouTube URL Input */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-8">
        <div className="flex items-center gap-3 mb-6">
          <Link className="w-8 h-8 text-red-400" />
          <h3 className="text-xl font-bold text-white">
            Add YouTube Video
          </h3>
        </div>

        <form onSubmit={handleYouTubeSubmit} className="space-y-4">
          <div>
            <input
              type="url"
              value={youtubeUrl}
              onChange={(e) => setYoutubeUrl(e.target.value)}
              placeholder="https://www.youtube.com/watch?v=..."
              className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          <button
            type="submit"
            disabled={!youtubeUrl.trim()}
            className="w-full bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center gap-2"
          >
            <Play className="w-5 h-5" />
            Load YouTube Video
          </button>
        </form>

        <div className="mt-4 p-3 bg-blue-500/20 rounded-lg flex items-start gap-2">
          <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-blue-200">
            YouTube videos will be processed for transcript extraction and AI analysis
          </p>
        </div>
      </div>
    </div>
  );
};

export default VideoUpload;
