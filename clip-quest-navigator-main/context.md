 
Them: Analysis, we're gonna learn how to chat with videos, site videos, and search videos. Let's go. Okay. So I'm just gonna go through the background, the scenario, and then some of the projects. The team. Like, as the as they progress. So really quickly about me, my background is I work in Big Tech for a while, so I was on Microsoft Google. After that, I started a consulting business called Exaflop. And now I'm working at this company called DoubleSpeed where we make a add a powered video editor. Yeah. So here's the scenario. Basically, we have a lot of video data, podcasts with hours of footage, vlogs with tens of minutes of footage, shorts with seconds of footage. We want an effective way to query and search across video, supporting kind of all types of videos.  
Me: So  
Them: So  
Me: the first  
Them: the first thing we wanna handle is, like, what is this video about? And that's called, like, this is, like, very very simple rag. You might chat with, like, a book or a PDF. We wanna chat with a video. So we wanna be able to upload a video or add a YouTube video link. Then I wanna have, like, a chat interface with the video where I can say, like, what's this video about? Or if it's, a video about like, science, I can ask, like, what is what are the contents of this video? Like, what is tell me more about science and things like that. And the answers will come from Rag across the video. And, basically, it'll be able to answer questions about its content. The next level is, like, I want it to the chat application to be able to take me to a specific video sections. So a lot of times you might see a podcast, they'll have some sort of, like, time stamps and outlines of a video, kind of like the little sections on YouTube. So when I upload a video, I should get, like, a section breakdown with hyperlink time stamps. Then when I chat with a video, the portions of the video reference should have timestamp hyperlinked citations. So for example, if you uploaded this video, I and you ask, like, what's this video about? Maybe you'd get a response like Hassan presented the video project. Then you'd have a time stamp of me presenting the video project. Maybe it's at, like, second 10. You click that and you jump to section 10. Section second 10. Where it would be me talking about the video project.  
Me: And then I'd say, which  
Them: It might say, included a portion on citations. And then that could be another type time time stamp. That it would jump to. And then the final part of the project is search. So I wanna be able to, via natural language, query video. I wanna be able to query videos by the contents within the frame. Right? So if I say red car, I wanna pull up the clips where a red car is in the frame of the video I've uploaded. So their requirements for this one are kind of, like, given a video except a natural language query. Of the contents of a frame. And show the clips of the video that match the user query. Right? So I might upload a full video of a car show and if I only care about the red cars, I should be able to type in red car and should be able to jump across all those points where a red car is in the in the frame. Jump across all those clips.  
Me: So yeah.  
Them: So, yeah, that's kind of the project for this week. I appreciate you all tuning in, and have a good one. 