#!/usr/bin/env python3
"""
Test with a new YouTube video to verify frame creation
"""
import requests
import time

BACKEND_URL = "http://localhost:8000"

def test_new_youtube_video():
    """Test processing a new YouTube video"""
    try:
        # Use a different short video
        test_url = "https://www.youtube.com/watch?v=jNQXAC9IVRw"  # "Me at the zoo" - first YouTube video
        
        payload = {"youtube_url": test_url}
        response = requests.post(
            f"{BACKEND_URL}/api/v1/video/youtube", 
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ New YouTube Video Processing:")
            print(f"   Video ID: {data.get('video_id')}")
            print(f"   Title: {data.get('title')}")
            print(f"   Has Transcript: {data.get('has_transcript')}")
            print(f"   Sections: {data.get('sections_count')}")
            return data.get('video_id')
        else:
            print(f"❌ YouTube processing failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ YouTube processing error: {e}")
        return None

def check_video_details(video_id):
    """Check video details including frames"""
    if not video_id:
        return
        
    try:
        # Get video info
        response = requests.get(f"{BACKEND_URL}/api/v1/video/{video_id}", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"\n📹 Video {video_id} Details:")
            print(f"   Title: {data.get('title')}")
            print(f"   Status: {data.get('status')}")
            print(f"   Frame Count: {data.get('frame_count')}")
            
        # Get frames
        response = requests.get(f"{BACKEND_URL}/api/v1/search/{video_id}/frames", timeout=10)
        if response.status_code == 200:
            data = response.json()
            frames = data.get('frames', [])
            print(f"   Actual Frames Found: {len(frames)}")
            for frame in frames[:3]:
                print(f"   - {frame.get('timestamp')}s: {frame.get('description', 'No description')}")
            return len(frames)
        else:
            print(f"   ❌ Failed to get frames: {response.status_code}")
            return 0
            
    except Exception as e:
        print(f"   ❌ Error checking details: {e}")
        return 0

def test_chat_with_new_video(video_id):
    """Test chat with the new video"""
    if not video_id:
        return False
        
    try:
        payload = {
            "video_id": video_id,
            "message": "What is this video about?"
        }
        response = requests.post(
            f"{BACKEND_URL}/api/v1/chat/message",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n💬 Chat with Video {video_id}:")
            print(f"   Response: {data.get('response')[:150]}...")
            print(f"   Citations: {len(data.get('citations', []))}")
            return True
        else:
            print(f"   ❌ Chat failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Chat error: {e}")
        return False

def test_visual_search_with_new_video(video_id):
    """Test visual search with the new video"""
    if not video_id:
        return False
        
    try:
        payload = {
            "video_id": video_id,
            "query": "person",
            "max_results": 3
        }
        response = requests.post(
            f"{BACKEND_URL}/api/v1/search/visual",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n🔍 Visual Search in Video {video_id}:")
            print(f"   Query: {data.get('query')}")
            print(f"   Results: {data.get('total_results')}")
            for result in data.get('results', [])[:3]:
                print(f"   - {result.get('timestamp')}s: {result.get('confidence'):.1f}% - {result.get('description')}")
            return True
        else:
            print(f"   ❌ Visual search failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Visual search error: {e}")
        return False

def main():
    print("🆕 Testing New YouTube Video Processing")
    print("=" * 45)
    
    # Process new video
    video_id = test_new_youtube_video()
    
    if video_id:
        # Wait for processing
        print("\n⏳ Waiting for processing to complete...")
        time.sleep(2)
        
        # Check details
        frame_count = check_video_details(video_id)
        
        # Test chat
        chat_success = test_chat_with_new_video(video_id)
        
        # Test visual search
        search_success = test_visual_search_with_new_video(video_id)
        
        print("\n" + "=" * 45)
        print("📊 New Video Test Summary:")
        print(f"✅ Video Processing: {'Success' if video_id else 'Failed'}")
        print(f"✅ Frames Created: {frame_count}")
        print(f"✅ Chat Working: {'Yes' if chat_success else 'No'}")
        print(f"✅ Visual Search: {'Yes' if search_success else 'No'}")
        
        if frame_count > 0 and chat_success and search_success:
            print("\n🎉 All features working! VideoChat AI is fully operational!")
        else:
            print("\n⚠️  Some features need attention.")
    else:
        print("\n❌ Failed to process new video")

if __name__ == "__main__":
    main()
